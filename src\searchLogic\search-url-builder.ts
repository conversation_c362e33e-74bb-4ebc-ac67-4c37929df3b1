class SearchUrlBuilder {
    /**
     * Constructs a GitHub search URL.
     * @param {string[]} serviceKeywords - Keywords for the service.
     * @param {string} section - The search section (e.g., "code").
     * @param {number} page - The page number for pagination.
     * @returns {string} The constructed search URL.
     */
    public constructSearchURL(serviceKeywords: string[], fileType: string, section: string, page: number = 1): string {
        const query =
            "(" + fileType + ")" + " AND " + serviceKeywords.join(" AND "); // Add quotes for exact phrase matching
        const encodedQuery = encodeURIComponent(query).replace(/%20/g, "+"); // Replace %20 with + for GitHub search
        const query_url = `https://github.com/search?q=${encodedQuery}&type=${section}&p=${page}`;
        return query_url;
    }
}

export default SearchUrlBuilder;