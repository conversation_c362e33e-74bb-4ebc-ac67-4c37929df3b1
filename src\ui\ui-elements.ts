// ui-elements.ts
// 负责 UI 元素引用和状态变量的定义

export class UIElements {
    // --- UI State Variables ---
    static controlPanelVisible: boolean = true; // Visibility state of the control panel
    static panelContentVisible: boolean = true; // Visibility state of the panel's main content

    // --- UI Element References ---
    static controlPanelElement: HTMLDivElement | null = null;
    static keysDisplayElement: HTMLDivElement | null = null;
    static statusElement: HTMLDivElement | null = null;
    static apiKeyTypeSelectElement: HTMLSelectElement;
    static apiKeyTypeSelectContainer: HTMLDivElement | null = null;
    static progressElement: HTMLDivElement | null = null;
    static togglePanelContentButton: HTMLButtonElement | null = null;
    static searchButtonElement: HTMLButtonElement | null = null;
}