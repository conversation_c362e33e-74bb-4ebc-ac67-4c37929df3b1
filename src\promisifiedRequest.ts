import { GM_xmlhttpRequest } from "$";
interface RequestOptions {
  method?: string;
  url: string;
  headers?: Record<string, string>;
  data?: any;
  timeout?: number;
  ontimeout?: () => void;
}

export function promisifiedRequest(options: RequestOptions): Promise<any> {
  return new Promise((resolve, reject) => {
    GM_xmlhttpRequest({
      ...options,
      onload: function (response: any) {
        resolve(response);
      },
      onerror: function (error: any) {
        reject(error);
      },
      ontimeout: function () {
        reject("timeout");
      },
    });
  });
}

export default promisifiedRequest;
