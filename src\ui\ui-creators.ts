// ui-creators.ts
// 负责 UI 元素的创建

import PANEL_STYLES from '../styles';
import { AI_SERVICES } from "../constants";
import { UIElements } from "./ui-elements";
import { UIHandlers } from './ui-handlers';// 负责 UI 元素的创建

export class UICreators {
    /**
     * Applies CSS styles for the control panel to the document head.
     */
    static applyStyles = (): void => {
        const style: HTMLStyleElement = document.createElement("style");
        style.textContent = PANEL_STYLES;
        document.head.appendChild(style);
    }

    /**
     * Creates and returns a button element.
     * @param {string} text - The button text.
     * @param {function} onClickHandler - The event handler for button click.
     * @param {string} [className] - Optional CSS class name.
     * @returns {HTMLButtonElement} The created button element.
     */
    static createButton = (text: string, onClickHandler: () => void, className?: string): HTMLButtonElement => {
        const button: HTMLButtonElement = document.createElement("button");
        button.textContent = text;
        button.addEventListener("click", onClickHandler);
        if (className) {
            button.className = className;
        }
        return button;
    }

    /**
     * Creates the API type selection dropdown.
     * @returns {HTMLDivElement} The div containing the label and select element.
     */
    static createApiKeyTypeSelect = (): HTMLDivElement => {
        const selectDiv: HTMLDivElement = document.createElement("div");
        selectDiv.style.marginBottom = "10px";
        selectDiv.style.textAlign = "center";

        const label: HTMLLabelElement = document.createElement("label");
        label.textContent = "选择API类型: ";
        label.htmlFor = "apiKeyTypeSelect";
        selectDiv.appendChild(label);

        UIElements.apiKeyTypeSelectElement = document.createElement("select");
        UIElements.apiKeyTypeSelectElement.id = "apiKeyTypeSelect";
        UIElements.apiKeyTypeSelectElement.style.padding = "5px";
        UIElements.apiKeyTypeSelectElement.style.borderRadius = "3px";

        const allOption: HTMLOptionElement = document.createElement("option");
        allOption.value = "ALL";
        allOption.textContent = "全部类型";
        UIElements.apiKeyTypeSelectElement.appendChild(allOption);

        AI_SERVICES.forEach((service: { name: string }) => {
            const option: HTMLOptionElement = document.createElement("option");
            option.value = service.name;
            option.textContent = service.name;
            UIElements.apiKeyTypeSelectElement.appendChild(option);
        });
        // 设置默认选中项为 "Gemini"
        if (UIElements.apiKeyTypeSelectElement) {
            UIElements.apiKeyTypeSelectElement.value = "Gemini";
            selectDiv.appendChild(UIElements.apiKeyTypeSelectElement);
        }
        return selectDiv;
    }

    /**
     * Creates and returns the panel title element.
     * @returns {HTMLHeadingElement} The panel title element.
     */
    static createPanelTitle = (): HTMLHeadingElement => {
        const title: HTMLHeadingElement = document.createElement("h3");
        title.textContent = "GitHub API 密钥查找器";
        return title;
    }

    /**
     * Creates and returns the button to toggle panel content visibility.
     * Also assigns the button to togglePanelContentButton.
     * @returns {HTMLButtonElement} The toggle button element.
     */
    static createPanelToggleButton = (): HTMLButtonElement => {
        UIElements.togglePanelContentButton = UICreators.createButton(
            UIElements.panelContentVisible ? "隐藏面板" : "显示面板",
            UIHandlers.handleTogglePanelContent,
            "toggle-panel-button" // Add this class
        );
        UIElements.togglePanelContentButton.style.width = "100%";
        UIElements.togglePanelContentButton.style.marginBottom = "10px";
        return UIElements.togglePanelContentButton;
    }

    /**
     * Creates and returns the API key display area element.
     * Also assigns the element to keysDisplayElement.
     * @returns {HTMLDivElement} The key display area element.
     */
    static createKeyDisplayArea = (): HTMLDivElement => {
        UIElements.keysDisplayElement = document.createElement("div");
        UIElements.keysDisplayElement.id = "apiKeyDisplayArea";
        UIElements.keysDisplayElement.classList.add("panel-content-toggleable");
        UIElements.keysDisplayElement.dataset.initialDisplay = "block";
        return UIElements.keysDisplayElement;
    }

    /**
     * Creates and returns the container for the API key type selection dropdown.
     * Reuses createApiKeyTypeSelect and assigns the container to apiKeyTypeSelectContainer.
     * @returns {HTMLDivElement} The select container element.
     */
    static createApiKeySelectArea = (): HTMLDivElement => {
        UIElements.apiKeyTypeSelectContainer = UICreators.createApiKeyTypeSelect();
        UIElements.apiKeyTypeSelectContainer.classList.add("panel-content-toggleable");
        UIElements.apiKeyTypeSelectContainer.dataset.initialDisplay = "block";
        return UIElements.apiKeyTypeSelectContainer;
    }

    /**
     * Creates and returns the actions button area.
     * Includes buttons for running search, copying keys, clearing keys, and copying debug info.
     * @returns {HTMLDivElement} The actions area element.
     */
    static createActionsArea = (): HTMLDivElement => {
        const actionsDiv: HTMLDivElement = document.createElement("div");
        actionsDiv.className = "actions panel-content-toggleable";
        actionsDiv.dataset.initialDisplay = "flex";
        actionsDiv.style.textAlign = "center";
        UIElements.searchButtonElement = UICreators.createButton("搜索", UIHandlers.handleRunSearch, undefined); // Assign to variable
        actionsDiv.appendChild(UIElements.searchButtonElement); // Append the variable
        actionsDiv.appendChild(UICreators.createButton("复制", UIHandlers.handleCopyKeys, undefined));
        actionsDiv.appendChild(UICreators.createButton("清空", UIHandlers.handleClearKeys, "clear"));
        actionsDiv.appendChild(UICreators.createButton("调试", UIHandlers.handleCopyDebugInfo, undefined));
        return actionsDiv;
    }

    /**
     * Creates and returns the progress display element.
     * Also assigns the element to progressElement.
     * @returns {HTMLDivElement} The progress element.
     */
    static createProgressArea = (): HTMLDivElement => {
        UIElements.progressElement = document.createElement("div");
        UIElements.progressElement.id = "apiKeyFinderProgress";
        UIElements.progressElement.textContent = "准备就绪";
        UIElements.progressElement.classList.add("panel-content-toggleable");
        UIElements.progressElement.dataset.initialDisplay = "block";
        return UIElements.progressElement;
    }

    /**
     * Creates and returns the status display element.
     * Also assigns the element to statusElement.
     * @returns {HTMLDivElement} The status element.
     */
    static createStatusArea = (): HTMLDivElement => {
        UIElements.statusElement = document.createElement("div");
        UIElements.statusElement.id = "apiKeyFinderStatus";
        UIElements.statusElement.classList.add("panel-content-toggleable");
        UIElements.statusElement.dataset.initialDisplay = "block";
        return UIElements.statusElement;
    }
}
