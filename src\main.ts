import { GM_setValue, GM_registerMenuCommand } from '$';
import { debugLog } from "./debuglog";
import { UI } from "./ui";
import { mainSearchLogic } from "./searchLogic/main-search";

(function () {
  "use strict";

  // --- Initialization ---
  function initialize() {
    // Clear newly found keys from the previous session at the start of the script
    GM_setValue("newlyFoundApiKeys", []);
    debugLog("[+] 清空了本次运行新发现的密钥存储。", "general");

    UI.applyStyles();
    const controlPanelElement = UI.createControlPanel(); // This now also sets up panelContentElements
    document.body.appendChild(controlPanelElement);
    GM_registerMenuCommand("切换 API 密钥查找器面板", UI.toggleControlPanel);
    mainSearchLogic("ALL"); // Initial display of keys

    // Set initial panel content visibility
    // UI.panelContentVisible = false; // Default to hide content - This state is now managed within the UI module
    // UI.updatePanelContentVisibility(); // Apply the visibility state

    debugLog("API 密钥查找器 UI 已初始化。", "general");
  }

  // Ensure script runs after the DOM is fully loaded
  if (
    document.readyState === "complete" ||
    document.readyState === "interactive"
  ) {
    initialize();
  } else {
    window.addEventListener("DOMContentLoaded", initialize);
  }
})();
