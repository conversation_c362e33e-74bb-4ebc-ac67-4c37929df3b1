class SearchState {
    private stopSearchFlag: boolean = false;
    private currentSessionKeys: Set<string> = new Set();
    private totalFilesToProcess: number = 0;
    private processedFilesCount: number = 0;

    public resetProgress(): void {
        this.totalFilesToProcess = 0;
        this.processedFilesCount = 0;
    }

    public setStopSearchFlag(value: boolean): void {
        this.stopSearchFlag = value;
    }

    public getStopSearchFlag(): boolean {
        return this.stopSearchFlag;
    }

    public clearCurrentSessionKeys(): void {
        this.currentSessionKeys.clear();
    }

    public addCurrentSessionKey(key: string): void {
        this.currentSessionKeys.add(key);
    }

    public getCurrentSessionKeys(): Set<string> {
        return this.currentSessionKeys;
    }

    public setTotalFilesToProcess(value: number): void {
        this.totalFilesToProcess = value;
    }

    public setProcessedFilesCount(value: number): void {
        this.processedFilesCount = value;
    }

    public getTotalFilesToProcess(): number {
        return this.totalFilesToProcess;
    }

    public getProcessedFilesCount(): number {
        return this.processedFilesCount;
    }
}

export default SearchState;