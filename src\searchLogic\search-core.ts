import { debugLog } from "../debuglog";
import { GM_getValue } from '$';
import { UI } from "../ui";
import SearchState from "./search-state";
import { promisifiedRequest } from "../promisifiedRequest";
import KeyExtractor from "./key-extraction";
import { API_KEY_PATTERNS } from "../constants";


const searchState = new SearchState();

/**
 * Processes a single search result item to extract raw file URL and fetch content.
 * @param {object} item - The search result item object.
 * @param {string} serviceName - The name of the AI service being searched for.
 * @returns {Promise<void>}
  */
export async function processSearchResultItem(item: any, serviceName: string) {
    if (searchState.getStopSearchFlag()) {
        debugLog(`停止标志已设置，跳过处理搜索结果项: ${item.path || "未知路径"}`, "general");
        return;
    }

    const repoNwo = item.repo_nwo;
    const filePath = item.path;
    const commitSha = item.commit_sha || item.ref_name; // Fallback to ref_name (branch/tag)

    if (repoNwo && filePath && commitSha) {
        const rawUrl = `https://raw.githubusercontent.com/${repoNwo}/${commitSha}/${filePath}`;
        debugLog(
            `构造 Raw URL: ${rawUrl} (repo: ${repoNwo}, path: ${filePath}, commit: ${commitSha})`,
            "rawUrlConstructed"
        );
        await fetchAndProcessRawFile(rawUrl, serviceName, filePath);
    } else {
        debugLog(
            `搜索结果条目信息不全，无法构造 Raw URL: ${JSON.stringify(
                item
            ).substring(0, 200)}...`,
            "error"
        );
        // Increment processedFilesCount even if we can't process the item
        searchState.setProcessedFilesCount(searchState.getProcessedFilesCount() + 1);
        UI.updateProgressDisplay(
            `已跳过 (信息不全): ${searchState.getProcessedFilesCount()} / ${searchState.getTotalFilesToProcess()} - ${filePath ? filePath.split("/").pop() : "未知文件"
            }`
        );
    }
}

/**
 * Handles the logic for found potential keys: deduplication and storage.
 * @param {string[]} potentialKeys - An array of potential key strings found in a file.
 * @param {string} serviceName - The name of the AI service the keys belong to.
 */
function handleFoundKeys(potentialKeys: string[], serviceName: string) {
    if (potentialKeys.length === 0) {
        debugLog(`未找到 ${serviceName} 的潜在密钥。`, "general");
        return;
    }

    debugLog(
        `找到 ${potentialKeys.length
        } 个潜在密钥为 ${serviceName}: ${potentialKeys.join(", ")}`,
        "general"
    );

    // Get current long-term stored keys for this service
    const longTermStorageKey = `${serviceName
        .charAt(0)
        .toLowerCase()}${serviceName.slice(1)}ApiKeys`;
    let longTermKeys: string[] = GM_getValue(longTermStorageKey, []);
    const longTermKeysSet = new Set(longTermKeys); // Use a Set for efficient lookup

    // Get current newly found keys for this session
    let newlyFoundKeys: string[] = GM_getValue("newlyFoundApiKeys", []);
    const newlyFoundKeysSet = new Set(newlyFoundKeys); // Use a Set for efficient lookup

    let newKeysFoundInThisFile = 0;

    potentialKeys.forEach((key) => {
        // Check if the key is already in the long-term storage
        if (!longTermKeysSet.has(key)) {
            // It's a new key not seen before (in long-term storage)
            debugLog(`发现新密钥 (不在长期存储中): ${key}`, "general");

            // Add to long-term storage (in-memory array and Set)
            longTermKeys.push(key);
            longTermKeysSet.add(key); // Update the Set for subsequent checks in this file

            // Add to newly found keys for this session (in-memory array and Set)
            if (!newlyFoundKeysSet.has(key)) {
                // Ensure it's not already added in this session from another file
                newlyFoundKeys.push(key);
                newlyFoundKeysSet.add(key);
            }

            // Add to the cumulative session set (used by UI update)
            // currentSessionKeys.add(key);  //TODO: fix this

            newKeysFoundInThisFile++;
        } else {
            debugLog(`密钥已存在于长期存储中，忽略: ${key}`, "general");
        }
    });

    // If any new keys were found, update storage and UI
    if (newKeysFoundInThisFile > 0) {
        debugLog(
            `找到 ${newKeysFoundInThisFile} 个新密钥，更新存储。`,
            "general"
        );
        // Update long-term storage for this category
        GM_setValue(longTermStorageKey, longTermKeys);
        debugLog(
            `更新了长期存储 (${longTermStorageKey})，总数: ${longTermKeys.length}`,
            "general"
        );

        // Update newly found keys storage for this session
        GM_setValue("newlyFoundApiKeys", newlyFoundKeys);
        debugLog(
            `更新了本次新发现密钥存储，总数: ${newlyFoundKeys.length}`,
            "general"
        );

        // Update the UI display immediately to show the new total count
        UI.updateKeyDisplay();
    } else {
        debugLog(`未找到新密钥。`, "general");
    }
}

/**
 * Fetches and processes a single raw file content from GitHub.
 * @param {string} rawUrl - The URL of the raw file.
 * @param {string} serviceName - The name of the AI service being searched for.
 * @param {string} filePath - The path of the file (for logging).
 * @returns {Promise<void>}
 */
export async function fetchAndProcessRawFile(rawUrl: string, serviceName: string, filePath: string) {
    // Check stop flag before making the request
    if (searchState.getStopSearchFlag()) {
        debugLog(`停止标志已设置，跳过获取文件: ${filePath}`, "general");
        searchState.setProcessedFilesCount(searchState.getProcessedFilesCount() + 1); // Still count it as processed to update progress correctly
        UI.updateProgressDisplay(
            `已跳过 (停止): ${searchState.getProcessedFilesCount()} / ${searchState.getTotalFilesToProcess()} - ${filePath
                .split("/")
                .pop()}`
        );
        return Promise.resolve(); // Resolve immediately if stopping
    }

    debugLog(`开始获取原始文件: ${rawUrl}`, "fileFetch");
    const fileName = filePath.split("/").pop();
    UI.updateProgressDisplay(
        `尝试获取: ${searchState.getProcessedFilesCount() + 1
        } / ${searchState.getTotalFilesToProcess()} - ${fileName}`
    );

    try {
        const response = await promisifiedRequest({
            method: "GET",
            url: rawUrl,
            timeout: 10000, // Add 10-second timeout
        });

        // Check stop flag after successful request but before processing
        if (searchState.getStopSearchFlag()) {
            debugLog(
                `停止标志已设置，获取成功但跳过处理文件: ${filePath}`,
                "general"
            );
            // processedFilesCount is incremented below in finally
            // UI.updateProgressDisplay is done in finally
            return; // Exit the async function
        }

        searchState.setProcessedFilesCount(searchState.getProcessedFilesCount() + 1);
        if (response.status >= 200 && response.status < 300) {
            const rawContent = response.responseText;
            debugLog(
                `成功获取 ${filePath} (前200字符): ${rawContent.substring(
                    0,
                    200
                )}...`,
                "fileFetch"
            );
            const keyExtractor = new KeyExtractor();
            const potentialKeys: string[] = keyExtractor.extractPotentialKeys(rawContent, serviceName as keyof typeof API_KEY_PATTERNS);
            handleFoundKeys(potentialKeys, serviceName);
        } else {
            debugLog(
                `获取 ${filePath} 失败。状态: ${response.status}, URL: ${rawUrl}`,
                "error"
            );
        }
    } catch (error) {
        // Handle network errors, timeouts, etc.
        if (searchState.getStopSearchFlag()) {
            debugLog(
                `停止标志已设置，获取出错但跳过处理文件: ${filePath}`,
                "general"
            );
            // processedFilesCount is incremented below in finally
            // UI.updateProgressDisplay is done in finally
            return; // Exit the async function
        }
        debugLog(
            `获取 ${filePath} 时出错. URL: ${rawUrl}, 错误: ${(error as any).statusText || (error as any).message || "未知"
            }`,
            "error"
        );
        // processedFilesCount is incremented below in finally
        // UI.updateProgressDisplay is done in finally
    } finally {
        // Always increment processedFilesCount and update progress
        searchState.setProcessedFilesCount(searchState.getProcessedFilesCount() + 1);
        UI.updateProgressDisplay(
            `已处理: ${searchState.getProcessedFilesCount()} / ${searchState.getTotalFilesToProcess()} - ${fileName}`
        );
    }
}

export { handleFoundKeys };