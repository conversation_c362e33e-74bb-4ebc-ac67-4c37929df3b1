import { debugLog } from "../debuglog";
import { UI } from "../ui";
import { promisifiedRequest } from "../promisifiedRequest";
import KeyExtractor from "./key-extraction";
import { API_KEY_PATTERNS } from "../constants";
import { SearchLogic } from "../searchLogic";
import { searchState } from "./main-search";

/**
 * Processes a single search result item to extract raw file URL and fetch content.
 * @param {object} item - The search result item object.
 * @param {string} serviceName - The name of the AI service being searched for.
 * @returns {Promise<void>}
  */
export async function processSearchResultItem(item: any, serviceName: string) {
    if (searchState.getStopSearchFlag()) {
        debugLog(`停止标志已设置，跳过处理搜索结果项: ${item.path || "未知路径"}`, "general");
        return;
    }

    const repoNwo = item.repo_nwo;
    const filePath = item.path;
    const commitSha = item.commit_sha || item.ref_name; // Fallback to ref_name (branch/tag)

    if (repoNwo && filePath && commitSha) {
        const rawUrl = `https://raw.githubusercontent.com/${repoNwo}/${commitSha}/${filePath}`;
        debugLog(
            `构造 Raw URL: ${rawUrl} (repo: ${repoNwo}, path: ${filePath}, commit: ${commitSha})`,
            "rawUrlConstructed"
        );
        await fetchAndProcessRawFile(rawUrl, serviceName, filePath);
    } else {
        debugLog(
            `搜索结果条目信息不全，无法构造 Raw URL: ${JSON.stringify(
                item
            ).substring(0, 200)}...`,
            "error"
        );
        // Increment processedFilesCount even if we can't process the item
        searchState.setProcessedFilesCount(searchState.getProcessedFilesCount() + 1);
        UI.updateProgressDisplay(
            `已跳过 (信息不全): ${searchState.getProcessedFilesCount()} / ${searchState.getTotalFilesToProcess()} - ${filePath ? filePath.split("/").pop() : "未知文件"
            }`
        );
    }
}



/**
 * Fetches and processes a single raw file content from GitHub.
 * @param {string} rawUrl - The URL of the raw file.
 * @param {string} serviceName - The name of the AI service being searched for.
 * @param {string} filePath - The path of the file (for logging).
 * @returns {Promise<void>}
 */
export async function fetchAndProcessRawFile(rawUrl: string, serviceName: string, filePath: string) {
    // Check stop flag before making the request
    if (searchState.getStopSearchFlag()) {
        debugLog(`停止标志已设置，跳过获取文件: ${filePath}`, "general");
        searchState.setProcessedFilesCount(searchState.getProcessedFilesCount() + 1); // Still count it as processed to update progress correctly
        UI.updateProgressDisplay(
            `已跳过 (停止): ${searchState.getProcessedFilesCount()} / ${searchState.getTotalFilesToProcess()} - ${filePath
                .split("/")
                .pop()}`
        );
        return Promise.resolve(); // Resolve immediately if stopping
    }

    debugLog(`开始获取原始文件: ${rawUrl}`, "fileFetch");
    const fileName = filePath.split("/").pop();
    UI.updateProgressDisplay(
        `尝试获取: ${searchState.getProcessedFilesCount() + 1
        } / ${searchState.getTotalFilesToProcess()} - ${fileName}`
    );

    try {
        const response = await promisifiedRequest({
            method: "GET",
            url: rawUrl,
            timeout: 10000, // Add 10-second timeout
        });

        // Check stop flag after successful request but before processing
        if (searchState.getStopSearchFlag()) {
            debugLog(
                `停止标志已设置，获取成功但跳过处理文件: ${filePath}`,
                "general"
            );
            // processedFilesCount is incremented below in finally
            // UI.updateProgressDisplay is done in finally
            return; // Exit the async function
        }

        searchState.setProcessedFilesCount(searchState.getProcessedFilesCount() + 1);
        if (response.status >= 200 && response.status < 300) {
            const rawContent = response.responseText;
            debugLog(
                `成功获取 ${filePath} (前200字符): ${rawContent.substring(
                    0,
                    200
                )}...`,
                "fileFetch"
            );
            const keyExtractor = new KeyExtractor();
            const potentialKeys: string[] = keyExtractor.extractPotentialKeys(rawContent, serviceName as keyof typeof API_KEY_PATTERNS);
            SearchLogic.handleFoundKeys(potentialKeys, serviceName as keyof typeof API_KEY_PATTERNS);
        } else {
            debugLog(
                `获取 ${filePath} 失败。状态: ${response.status}, URL: ${rawUrl}`,
                "error"
            );
        }
    } catch (error) {
        // Handle network errors, timeouts, etc.
        if (searchState.getStopSearchFlag()) {
            debugLog(
                `停止标志已设置，获取出错但跳过处理文件: ${filePath}`,
                "general"
            );
            // processedFilesCount is incremented below in finally
            // UI.updateProgressDisplay is done in finally
            return; // Exit the async function
        }
        debugLog(
            `获取 ${filePath} 时出错. URL: ${rawUrl}, 错误: ${(error as any).statusText || (error as any).message || "未知"
            }`,
            "error"
        );
        // processedFilesCount is incremented below in finally
        // UI.updateProgressDisplay is done in finally
    } finally {
        // Always increment processedFilesCount and update progress
        searchState.setProcessedFilesCount(searchState.getProcessedFilesCount() + 1);
        UI.updateProgressDisplay(
            `已处理: ${searchState.getProcessedFilesCount()} / ${searchState.getTotalFilesToProcess()} - ${fileName}`
        );
    }
}

