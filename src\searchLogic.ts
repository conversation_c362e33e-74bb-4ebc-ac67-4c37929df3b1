import { debugLog } from "./debuglog";
import { promisifiedRequest } from "./promisifiedRequest";
import { GM_setValue, GM_getValue } from '$';
import { FILE_TYPES, API_KEY_PATTERNS, AI_SERVICES, SEARCH_SECTION } from "./constants";
import { UI } from "./ui";
// --- Search Logic Module ---
export const SearchLogic = (function () {

    // --- Script State Variables ---
    let stopSearchFlag = false; // Flag to signal search stop
    const currentSessionKeys = new Set(); // Stores unique keys found during the current script execution

    // --- Statistics for Progress Tracking ---
    let totalFilesToProcess = 0;
    let processedFilesCount = 0;

    /**
     * Resets progress tracking variables.
     */
    function resetProgress() {
        totalFilesToProcess = 0;
        processedFilesCount = 0;
    }

    /**
     * Sets the stop search flag.
     * @param {boolean} value - The value to set the flag to.
     */
    function setStopSearchFlag(value: boolean) {
        stopSearchFlag = value;
    }

    /**
     * Gets the current value of the stop search flag.
     * @returns {boolean} The current value of the stop search flag.
     */
    function getStopSearchFlag() {
        return stopSearchFlag;
    }

    /**
     * Clears the current session keys set.
     */
    function clearCurrentSessionKeys() {
        currentSessionKeys.clear();
    }

    // --- Key Management Logic ---

    /**
     * Classifies a single API key based on known patterns, including specific AI services.
     * @param {string} key - The API key string.
     * @returns {string} The category of the key (e.g., "OpenAI", "Gemini", "Grok", "Claude").
     */
    function classifyKey(key: string) {
        if (typeof key !== "string") return "Other";

        for (const service of AI_SERVICES) {
            const patterns = API_KEY_PATTERNS[service.name as keyof typeof API_KEY_PATTERNS];
            if (patterns) {
                for (const regex of patterns) {
                    const testRegex = new RegExp(regex.source, regex.flags); // Ensure fresh regex for stateful flags like 'g'
                    if (testRegex.test(key)) {
                        return service.name; // This is the specific AI service name
                    }
                }
            }
        }
        return "Other";
    }

    /**
     * Retrieves all stored API keys, categorized including specific AI services.
     * @returns {Object<string, string[]>} An object where keys are category names
     *                                     (e.g., "GitHub", "AWS", "OpenAI", "Other")
     *                                     and values are arrays of key strings.
     */
    function getStoredCategorizedKeys() {
        const categories = AI_SERVICES.map((service) => service.name); // Only include AI service categories

        const categorizedKeys: { [key: string]: string[] } = {};
        categories.forEach((category: string) => {
            // For AI services like OpenAI, Claude, etc.
            // Stored as, e.g., openaiApiKeys, geminiApiKeys
            const storageKey = `${category.charAt(0).toLowerCase()}${category.slice(
                1
            )}ApiKeys`;
            categorizedKeys[category] = GM_getValue(storageKey, []);
        });
        // Optionally, you might want to explicitly add 'Other' if needed elsewhere,
        // but based on the task, we only care about the 4 AI types for storage/retrieval.
        // categorizedKeys["Other"] = GM_getValue("otherApiKeys", []); // Removed as per task

        return categorizedKeys;
    }

    // --- Core Search Logic ---

    /**
     * Processes a single search result item to extract raw file URL and fetch content.
     * @param {object} item - The search result item object.
     * @param {string} serviceName - The name of the AI service being searched for.
     * @returns {Promise<void>}
      */
    async function processSearchResultItem(item: any, serviceName: keyof typeof API_KEY_PATTERNS) {
        if (stopSearchFlag) {
            debugLog(`停止标志已设置，跳过处理搜索结果项: ${item.path || "未知路径"}`, "general");
            return;
        }

        const repoNwo = item.repo_nwo;
        const filePath = item.path;
        const commitSha = item.commit_sha || item.ref_name; // Fallback to ref_name (branch/tag)

        if (repoNwo && filePath && commitSha) {
            const rawUrl = `https://raw.githubusercontent.com/${repoNwo}/${commitSha}/${filePath}`;
            debugLog(
                `构造 Raw URL: ${rawUrl} (repo: ${repoNwo}, path: ${filePath}, commit: ${commitSha})`,
                "rawUrlConstructed"
            );
            await fetchAndProcessRawFile(rawUrl, serviceName, filePath);
        } else {
            debugLog(
                `搜索结果条目信息不全，无法构造 Raw URL: ${JSON.stringify(
                    item
                ).substring(0, 200)}...`,
                "error"
            );
            // Increment processedFilesCount even if we can't process the item
            processedFilesCount++;
            UI.updateProgressDisplay(
                `已跳过 (信息不全): ${processedFilesCount} / ${totalFilesToProcess} - ${filePath ? filePath.split("/").pop() : "未知文件"
                }`
            );
        }
    }

    /**
     * Handles the logic for found potential keys: deduplication and storage.
     * @param {string[]} potentialKeys - An array of potential key strings found in a file.
     * @param {string} serviceName - The name of the AI service the keys belong to.
     */
    function handleFoundKeys(potentialKeys: string[], serviceName: keyof typeof API_KEY_PATTERNS) {
        if (potentialKeys.length === 0) {
            debugLog(`未找到 ${serviceName} 的潜在密钥。`, "general");
            return;
        }

        debugLog(
            `找到 ${potentialKeys.length
            } 个潜在密钥为 ${serviceName}: ${potentialKeys.join(", ")}`,
            "general"
        );

        // Get current long-term stored keys for this service
        const longTermStorageKey = `${serviceName
            .charAt(0)
            .toLowerCase()}${serviceName.slice(1)}ApiKeys`;
        let longTermKeys: string[] = GM_getValue(longTermStorageKey, []);
        const longTermKeysSet = new Set(longTermKeys); // Use a Set for efficient lookup

        // Get current newly found keys for this session
        let newlyFoundKeys: string[] = GM_getValue("newlyFoundApiKeys", []);
        const newlyFoundKeysSet = new Set(newlyFoundKeys); // Use a Set for efficient lookup

        let newKeysFoundInThisFile = 0;

        potentialKeys.forEach((key) => {
            // Check if the key is already in the long-term storage
            if (!longTermKeysSet.has(key)) {
                // It's a new key not seen before (in long-term storage)
                debugLog(`发现新密钥 (不在长期存储中): ${key}`, "general");

                // Add to long-term storage (in-memory array and Set)
                longTermKeys.push(key);
                longTermKeysSet.add(key); // Update the Set for subsequent checks in this file

                // Add to newly found keys for this session (in-memory array and Set)
                if (!newlyFoundKeysSet.has(key)) {
                    // Ensure it's not already added in this session from another file
                    newlyFoundKeys.push(key);
                    newlyFoundKeysSet.add(key);
                }

                // Add to the cumulative session set (used by UI update)
                currentSessionKeys.add(key);

                newKeysFoundInThisFile++;
            } else {
                debugLog(`密钥已存在于长期存储中，忽略: ${key}`, "general");
            }
        });

        // If any new keys were found, update storage and UI
        if (newKeysFoundInThisFile > 0) {
            debugLog(
                `找到 ${newKeysFoundInThisFile} 个新密钥，更新存储。`,
                "general"
            );
            // Update long-term storage for this category
            GM_setValue(longTermStorageKey, longTermKeys);
            debugLog(
                `更新了长期存储 (${longTermStorageKey})，总数: ${longTermKeys.length}`,
                "general"
            );

            // Update newly found keys storage for this session
            GM_setValue("newlyFoundApiKeys", newlyFoundKeys);
            debugLog(
                `更新了本次新发现密钥存储，总数: ${newlyFoundKeys.length}`,
                "general"
            );

            // Update the UI display immediately to show the new total count
            UI.updateKeyDisplay();
        } else {
            debugLog(`未找到新密钥。`, "general");
        }
    }

    /**
     * Constructs a GitHub search URL.
     * @param {string[]} serviceKeywords - Keywords for the service.
     * @param {string} section - The search section (e.g., "code").
     * @param {number} page - The page number for pagination.
     * @returns {string} The constructed search URL.
     */
    function constructSearchURL(serviceKeywords: string[], fileType: string, section: string, page = 1) {
        const query =
            "(" + fileType + ")" + " AND " + serviceKeywords.join(" AND "); // Add quotes for exact phrase matching
        const encodedQuery = encodeURIComponent(query).replace(/%20/g, "+"); // Replace %20 with + for GitHub search
        const query_url = `https://github.com/search?q=${encodedQuery}&type=${section}&p=${page}`;
        return query_url;
    }

    /**
     * Extracts potential API keys from HTML content based on service-specific patterns.
     * @param {string} textContent - The text content to search within.
     * @param {string} serviceName - The name of the AI service.
     * @returns {string[]} An array of found potential keys.
     */
    function extractPotentialKeys(textContent: string, serviceName: keyof typeof API_KEY_PATTERNS): string[] {
        const patterns = API_KEY_PATTERNS[serviceName];
        if (!patterns) return [];

        let foundKeys = new Set<string>(); // Use a Set to automatically handle duplicates within this pass
        patterns.forEach((pattern) => {
            const currentRegex = new RegExp(pattern.source, pattern.flags); // Create a new instance
            let match;
            while ((match = currentRegex.exec(textContent)) !== null) {
                // Use the new instance
                foundKeys.add(match[0]);
            }
        });
        return Array.from(foundKeys);
    }

    /**
     * Fetches and processes a single raw file content from GitHub.
     * @param {string} rawUrl - The URL of the raw file.
     * @param {string} serviceName - The name of the AI service being searched for.
     * @param {string} filePath - The path of the file (for logging).
     * @returns {Promise<void>}
     */
    async function fetchAndProcessRawFile(rawUrl: string, serviceName: keyof typeof API_KEY_PATTERNS, filePath: string) {
        // Check stop flag before making the request
        if (stopSearchFlag) {
            debugLog(`停止标志已设置，跳过获取文件: ${filePath}`, "general");
            processedFilesCount++; // Still count it as processed to update progress correctly
            UI.updateProgressDisplay(
                `已跳过 (停止): ${processedFilesCount} / ${totalFilesToProcess} - ${filePath
                    .split("/")
                    .pop()}`
            );
            return Promise.resolve(); // Resolve immediately if stopping
        }

        debugLog(`开始获取原始文件: ${rawUrl}`, "fileFetch");
        const fileName = filePath.split("/").pop();
        UI.updateProgressDisplay(
            `尝试获取: ${processedFilesCount + 1
            } / ${totalFilesToProcess} - ${fileName}`
        );

        try {
            const response = await promisifiedRequest({
                method: "GET",
                url: rawUrl,
                timeout: 10000, // Add 10-second timeout
            });

            // Check stop flag after successful request but before processing
            if (stopSearchFlag) {
                debugLog(
                    `停止标志已设置，获取成功但跳过处理文件: ${filePath}`,
                    "general"
                );
                // processedFilesCount is incremented below in finally
                // UI.updateProgressDisplay is done in finally
                return; // Exit the async function
            }

            processedFilesCount++;
            if (response.status >= 200 && response.status < 300) {
                const rawContent = response.responseText;
                debugLog(
                    `成功获取 ${filePath} (前200字符): ${rawContent.substring(
                        0,
                        200
                    )}...`,
                    "fileFetch"
                );
                const potentialKeys: string[] = extractPotentialKeys(rawContent, serviceName);
                handleFoundKeys(potentialKeys, serviceName);
            } else {
                debugLog(
                    `获取 ${filePath} 失败。状态: ${response.status}, URL: ${rawUrl}`,
                    "error"
                );
            }
        } catch (error) {
            // Handle network errors, timeouts, etc.
            if (stopSearchFlag) {
                debugLog(
                    `停止标志已设置，获取出错但跳过处理文件: ${filePath}`,
                    "general"
                );
                // processedFilesCount is incremented below in finally
                // UI.updateProgressDisplay is done in finally
                return; // Exit the async function
            }
            debugLog(
                `获取 ${filePath} 时出错. URL: ${rawUrl}, 错误: ${(error as any).statusText || (error as any).message || "未知"
                }`,
                "error"
            );
            // processedFilesCount is incremented below in finally
            // UI.updateProgressDisplay is done in finally
        } finally {
            // Always increment processedFilesCount and update progress
            processedFilesCount++;
            UI.updateProgressDisplay(
                `已处理: ${processedFilesCount} / ${totalFilesToProcess} - ${fileName}`
            );
        }
    }

    /**
     * Performs a search for a given service in a specific section and page.
     * Extracts raw file URLs from search results and fetches their content.
     * @param {object} service - The AI service object.
     * @param {string} section - The search section.
     * @param {number} page - The page number.
     * @returns {Promise<boolean>} Resolves with true if search should continue, false if stopped or error.
     */
    async function performSingleSearch(service: { name: keyof typeof API_KEY_PATTERNS, keywords: string[] }, fileType: string, section: string, page = 1) {
        if (stopSearchFlag) {
            debugLog(
                `停止标志已设置，跳过搜索 ${service.name} ${fileType} 于 ${section} (页 ${page})`,
                "general"
            );
            return false; // Indicate that search should stop
        }

        debugLog(
            `开始搜索 ${service.name} ${fileType} 于 ${section} (页 ${page})`,
            "general"
        );
        const searchUrl = constructSearchURL(
            service.keywords,
            fileType,
            section,
            page
        );
        debugLog(`搜索 URL: ${searchUrl}`, "general");

        try {
            const response = await promisifiedRequest({
                method: "GET",
                url: searchUrl,
                // No timeout specified in original GM_xmlhttpRequest for search page
            });

            // Check stop flag after successful request but before processing
            if (stopSearchFlag) {
                debugLog(
                    `停止标志已设置，获取成功但跳过处理搜索结果页: ${searchUrl}`,
                    "general"
                );
                return false; // Indicate that search should stop
            }

            if (response.status >= 200 && response.status < 300) {
                const htmlContent = response.responseText;
                debugLog(
                    `GitHub 搜索结果 (前200字符): ${htmlContent.substring(0, 200)}...`,
                    "general"
                );

                let embeddedData = null;
                try {
                    const parser = new DOMParser();
                    const doc = parser.parseFromString(htmlContent, "text/html");
                    const scriptElement = doc.querySelector(
                        'script[type="application/json"][data-target="react-app.embeddedData"]'
                    );
                    if (scriptElement) {
                        embeddedData = JSON.parse(scriptElement.textContent || "{}");
                        debugLog(
                            {
                                message: "成功提取并解析 embeddedData JSON",
                                data: embeddedData,
                            },
                            "jsonExtract"
                        );
                    } else {
                        debugLog(
                            '未找到 <script data-target="react-app.embeddedData">。',
                            "error"
                        );
                    }
                } catch (e: any) {
                    debugLog(`解析 embeddedData JSON 失败: ${e.message}`, "error");
                }

                if (embeddedData?.payload?.results) {
                    debugLog(
                        `从 embeddedData 找到 ${embeddedData.payload.results.length} 个结果。`,
                        "general"
                    );
                    // Process each search result item
                    for (const item of embeddedData.payload.results) {
                        // IMPORTANT: This is the request delay logic.
                        await new Promise((resolveDelay) =>
                            setTimeout(resolveDelay, 2000 + Math.random() * 2000)
                        );

                        // Check stop flag before processing the next item
                        if (stopSearchFlag) {
                            debugLog(
                                `停止标志已设置，跳过处理搜索结果项: ${item.path}`,
                                "general"
                            );
                            break; // Exit the loop early
                        }
                        // Increment total files to process BEFORE processing the item
                        totalFilesToProcess++;
                        await processSearchResultItem(item, service.name);
                    }
                } else {
                    debugLog(
                        "embeddedData 中未找到 payload.results 或结构不符。",
                        "general"
                    );
                }

                // After processing all items in this page's results
                if (!stopSearchFlag && embeddedData?.payload?.results?.length > 0) {
                    debugLog(`当前页 (${page}) 的所有搜索结果项处理完成。`, "general");
                    // The totalFilesToProcess and processedFilesCount are updated within processSearchResultItem/fetchAndProcessRawFile
                } else if (
                    !stopSearchFlag &&
                    embeddedData?.payload?.results?.length === 0
                ) {
                    debugLog(`当前页 (${page}) 未找到搜索结果项。`, "general");
                } else if (stopSearchFlag) {
                    debugLog(
                        `因停止标志设置，当前页 (${page}) 的搜索结果处理被中断。`,
                        "general"
                    );
                }
                return !stopSearchFlag; // Resolve with false if stop flag is set
            } else {
                debugLog(
                    `获取 GitHub 搜索页失败. 状态: ${response.status}, URL: ${searchUrl}`,
                    "error"
                );
                return false; // Indicate search page fetch failure
            }
        } catch (error) {
            // Handle network errors from promisifiedRequest
            if (stopSearchFlag) {
                debugLog(
                    `停止标志已设置，获取 GitHub 搜索页时中断. URL: ${searchUrl}`,
                    "general"
                );
            } else {
                debugLog(
                    `获取 GitHub 搜索页时网络错误. URL: ${searchUrl}, 错误: ${(error as any).statusText || (error as any).message || "未知"
                    }`,
                    "error"
                );
            }
            return false; // Indicate failure or stop
        }
    }

    /**
     * Main logic for the API key search.
     * @param {string} selectedApiKeyType - The type of API key to search for ("ALL" or specific service name).
     */
    async function mainSearchLogic(selectedApiKeyType = "ALL") {
        debugLog(
            `[+] GitHub API 密钥查找器脚本启动。搜索类型: ${selectedApiKeyType}`,
            "general"
        );

        // 1. Load existing keys from storage into the current session
        const storedKeys = getStoredCategorizedKeys();
        let loadedKeyCount = 0;
        for (const category in storedKeys) {
            (storedKeys[category] as string[]).forEach((key) => {
                currentSessionKeys.add(key);
                loadedKeyCount++;
            });
        }
        if (loadedKeyCount > 0) {
            debugLog(`[+] 从本地存储加载了 ${loadedKeyCount} 个密钥。`, "general");
        }

        // 2. Determine which services to search
        let servicesToSearch = AI_SERVICES;
        if (selectedApiKeyType !== "ALL") {
            servicesToSearch = AI_SERVICES.filter(
                (s) => s.name === selectedApiKeyType
            );
            if (servicesToSearch.length === 0) {
                debugLog(
                    `[!] 未找到 "${selectedApiKeyType}" 服务配置，将搜索全部类型。`,
                    "error"
                );
                servicesToSearch = AI_SERVICES; // Fallback
            }
        }

        // 3. Perform searches
        for (const service of servicesToSearch) {
            if (stopSearchFlag) {
                debugLog("停止标志已设置，跳过服务搜索。", "general");
                break; // Exit service loop
            }
            const section = SEARCH_SECTION; // Use the constant directly
            for (const fileType of FILE_TYPES) {
                if (stopSearchFlag) {
                    debugLog("停止标志已设置，跳过文件类型搜索。", "general");
                    break; // Exit file type loop
                }
                // Loop through pages 1 to 5
                for (let page = 1; page <= 5; page++) {
                    if (stopSearchFlag) {
                        debugLog("停止标志已设置，跳过页面搜索。", "general");
                        break; // Exit page loop
                    }

                    // Status update is now handled by UI module
                    // if (statusElement) statusElement.textContent = `正在搜索 ${service.name} ${fileType} 于 ${section} (页 ${page})...`;
                    // updateProgressDisplay(`开始搜索 ${service.name} 于 ${section} (页 ${page})...`);

                    try {
                        // IMPORTANT: This is the request delay logic.
                        await new Promise((resolveDelay) => {
                            if (stopSearchFlag) {
                                debugLog("停止标志已设置，跳过延迟。", "general");
                                resolveDelay(undefined); // Resolve immediately if stopping
                            } else {
                                setTimeout(resolveDelay, 2000 + Math.random() * 2000); // Calculate random delay here
                            }
                        });

                        if (stopSearchFlag) {
                            debugLog(
                                "停止标志已设置，跳过 performSingleSearch。",
                                "general"
                            );
                            break; // Exit page loop
                        }

                        const success = await performSingleSearch(
                            service as { name: keyof typeof API_KEY_PATTERNS; keywords: string[]; },
                            fileType,
                            section,
                            page
                        );
                        if (!success) {
                            debugLog(
                                `搜索 ${service.name} ${fileType} 在 ${section} (页 ${page}) 失败或已停止，停止当前页循环。`,
                                "general"
                            );
                            break; // Stop the current page loop
                        }
                    } catch (error) {
                        if (stopSearchFlag) {
                            debugLog(
                                `停止标志已设置，搜索 ${service.name} ${fileType} 在 ${section} (页 ${page}) 时中断。`,
                                "general"
                            );
                        } else {
                            debugLog(
                                `搜索 ${service.name
                                } ${fileType} 在 ${section} (页 ${page}) 时发生未捕获错误: ${(error as any).message || error
                                }`,
                                "error"
                            );
                        }
                        // Even if there's an unexpected error here, we should probably stop the page loop
                        break;
                    }
                } // End page loop
            } // End file type loop
        } // End service loop

        if (stopSearchFlag) {
            debugLog("[+] 搜索任务因停止标志设置而提前结束。", "general");
        } else {
            debugLog("[+] 所有搜索任务已处理完毕。", "general");
        }

        // Saving is now handled incrementally in fetchAndProcessRawFile.
        // We only need to update the final status message here.

        // Final status update is now handled by UI module
        // const finalCategorizedKeys = getStoredCategorizedKeys();
        // let totalKeysCount = 0;
        // for (const category in finalCategorizedKeys) {
        //   totalKeysCount += finalCategorizedKeys[category].length;
        // }
        // const newlyFoundKeys = GM_getValue("newlyFoundApiKeys", []);
        // if (statusElement) {
        //   statusElement.textContent = stopSearchFlag
        //     ? `搜索已停止！已存储 ${totalKeysCount} 个密钥，本次新发现 ${newlyFoundKeys.length} 个。`
        //     : `搜索完成！已存储 ${totalKeysCount} 个密钥，本次新发现 ${newlyFoundKeys.length} 个。`;
        // }
        debugLog("[+] GitHub API 密钥查找器脚本结束。", "general");
    }

    return {
        resetProgress,
        setStopSearchFlag,
        getStopSearchFlag,
        clearCurrentSessionKeys,
        classifyKey,
        getStoredCategorizedKeys,
        mainSearchLogic,
    };
})();