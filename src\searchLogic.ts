import { debugLog } from "./debuglog";
import { GM_setValue, GM_getValue } from '$';
import { API_KEY_PATTERNS, AI_SERVICES } from "./constants";
import { UI } from "./ui";
// --- Search Logic Module ---
export const SearchLogic = (function () {

    // --- Script State Variables ---
    let stopSearchFlag = false; // Flag to signal search stop
    const currentSessionKeys = new Set(); // Stores unique keys found during the current script execution

    /**
     * Resets progress tracking variables.
     */
    function resetProgress() {
        // Progress tracking is now handled by SearchState class
    }

    /**
     * Sets the stop search flag.
     * @param {boolean} value - The value to set the flag to.
     */
    function setStopSearchFlag(value: boolean) {
        stopSearchFlag = value;
    }

    /**
     * Gets the current value of the stop search flag.
     * @returns {boolean} The current value of the stop search flag.
     */
    function getStopSearchFlag() {
        return stopSearchFlag;
    }

    /**
     * Clears the current session keys set.
     */
    function clearCurrentSessionKeys() {
        currentSessionKeys.clear();
    }

    // --- Key Management Logic ---

    /**
     * Classifies a single API key based on known patterns, including specific AI services.
     * @param {string} key - The API key string.
     * @returns {string} The category of the key (e.g., "OpenAI", "Gemini", "Grok", "Claude").
     */
    function classifyKey(key: string) {
        if (typeof key !== "string") return "Other";

        for (const service of AI_SERVICES) {
            const patterns = API_KEY_PATTERNS[service.name as keyof typeof API_KEY_PATTERNS];
            if (patterns) {
                for (const regex of patterns) {
                    const testRegex = new RegExp(regex.source, regex.flags); // Ensure fresh regex for stateful flags like 'g'
                    if (testRegex.test(key)) {
                        return service.name; // This is the specific AI service name
                    }
                }
            }
        }
        return "Other";
    }

    /**
     * Retrieves all stored API keys, categorized including specific AI services.
     * @returns {Object<string, string[]>} An object where keys are category names
     *                                     (e.g., "GitHub", "AWS", "OpenAI", "Other")
     *                                     and values are arrays of key strings.
     */
    function getStoredCategorizedKeys() {
        const categories = AI_SERVICES.map((service) => service.name); // Only include AI service categories

        const categorizedKeys: { [key: string]: string[] } = {};
        categories.forEach((category: string) => {
            // For AI services like OpenAI, Claude, etc.
            // Stored as, e.g., openaiApiKeys, geminiApiKeys
            const storageKey = `${category.charAt(0).toLowerCase()}${category.slice(
                1
            )}ApiKeys`;
            categorizedKeys[category] = GM_getValue(storageKey, []);
        });
        // Optionally, you might want to explicitly add 'Other' if needed elsewhere,
        // but based on the task, we only care about the 4 AI types for storage/retrieval.
        // categorizedKeys["Other"] = GM_getValue("otherApiKeys", []); // Removed as per task

        return categorizedKeys;
    }

    // --- Core Search Logic ---

    /**
     * Handles the logic for found potential keys: deduplication and storage.
     * @param {string[]} potentialKeys - An array of potential key strings found in a file.
     * @param {string} serviceName - The name of the AI service the keys belong to.
     */
    function handleFoundKeys(potentialKeys: string[], serviceName: keyof typeof API_KEY_PATTERNS) {
        if (potentialKeys.length === 0) {
            debugLog(`未找到 ${serviceName} 的潜在密钥。`, "general");
            return;
        }

        debugLog(
            `找到 ${potentialKeys.length
            } 个潜在密钥为 ${serviceName}: ${potentialKeys.join(", ")}`,
            "general"
        );

        // Get current long-term stored keys for this service
        const longTermStorageKey = `${serviceName
            .charAt(0)
            .toLowerCase()}${serviceName.slice(1)}ApiKeys`;
        let longTermKeys: string[] = GM_getValue(longTermStorageKey, []);
        const longTermKeysSet = new Set(longTermKeys); // Use a Set for efficient lookup

        // Get current newly found keys for this session
        let newlyFoundKeys: string[] = GM_getValue("newlyFoundApiKeys", []);
        const newlyFoundKeysSet = new Set(newlyFoundKeys); // Use a Set for efficient lookup

        let newKeysFoundInThisFile = 0;

        potentialKeys.forEach((key) => {
            // Check if the key is already in the long-term storage
            if (!longTermKeysSet.has(key)) {
                // It's a new key not seen before (in long-term storage)
                debugLog(`发现新密钥 (不在长期存储中): ${key}`, "general");

                // Add to long-term storage (in-memory array and Set)
                longTermKeys.push(key);
                longTermKeysSet.add(key); // Update the Set for subsequent checks in this file

                // Add to newly found keys for this session (in-memory array and Set)
                if (!newlyFoundKeysSet.has(key)) {
                    // Ensure it's not already added in this session from another file
                    newlyFoundKeys.push(key);
                    newlyFoundKeysSet.add(key);
                }

                // Add to the cumulative session set (used by UI update)
                currentSessionKeys.add(key);

                newKeysFoundInThisFile++;
            } else {
                debugLog(`密钥已存在于长期存储中，忽略: ${key}`, "general");
            }
        });

        // If any new keys were found, update storage and UI
        if (newKeysFoundInThisFile > 0) {
            debugLog(
                `找到 ${newKeysFoundInThisFile} 个新密钥，更新存储。`,
                "general"
            );
            // Update long-term storage for this category
            GM_setValue(longTermStorageKey, longTermKeys);
            debugLog(
                `更新了长期存储 (${longTermStorageKey})，总数: ${longTermKeys.length}`,
                "general"
            );

            // Update newly found keys storage for this session
            GM_setValue("newlyFoundApiKeys", newlyFoundKeys);
            debugLog(
                `更新了本次新发现密钥存储，总数: ${newlyFoundKeys.length}`,
                "general"
            );

            // Update the UI display immediately to show the new total count
            UI.updateKeyDisplay();
        } else {
            debugLog(`未找到新密钥。`, "general");
        }
    }







    return {
        resetProgress,
        setStopSearchFlag,
        getStopSearchFlag,
        clearCurrentSessionKeys,
        classifyKey,
        getStoredCategorizedKeys,
        handleFoundKeys,
    };
})();