
// --- Configuration Constants ---
export const FILE_TYPES = [
    "language:C",
    "language:C#",
    "language:C++",
    "language:Go",
    "language:HTML",
    "language:Java",
    "language:JavaScript",
    "language:JSX",
    "language:Kotlin",
    "language:Markdown",
    "language:PowerShell",
    "language:Python",
    "language:SQL",
    "language:Shell",
    "language:Swift",
    "language:TypeScript",
    "path:*.backup",
    "path:*.bak",
    "path:*.cfg",
    "path:*.conf",
    "path:*.config",
    "path:*.enc",
    "path:*.env",
    "path:*.envrc",
    "path:*.ini",
    "path:*.ipynb",
    "path:*.json",
    "path:*.key",
    "path:*.log",
    "path:*.private",
    "path:*.prod",
    "path:*.properties",
    "path:*.secret",
    "path:*.tmp",
    "path:*.toml",
    "path:*.txt",
    "path:*.xml",
    "path:*.yaml",
    "path:*.yml",
    "path:.ipynb_checkpoints",
];
export const COMMON_KEYWORDS = [
    "access_key",
    "secret_key",
    "access_token",
    "api_key",
    "apikey",
    "api_secret",
    "apiSecret",
    "app_secret",
    "application_key",
    "app_key",
    "appkey",
    "auth_token",
    "authsecret",
    "api",
    "key",
];


export const API_KEY_PATTERNS = {
    OpenAI: [
        /sk-[a-zA-Z0-9]{48}/g,
        /sk-proj-[a-zA-Z0-9]{24}\.[a-zA-Z0-9]{24}/g,
    ],
    Gemini: [/AIzaSy[a-zA-Z0-9_-]{33}/g],
    Grok: [/gk_[a-zA-Z0-9]{60}/g],
    Claude: [/sk-ant-(?:api03-)?[a-zA-Z0-9\-_]{80,120}/g],
};
export const AI_SERVICES = [
    { name: "OpenAI", keywords: ["openai api key", "sk-"] },
    {
        name: "Gemini",
        keywords: [
            // "(" + COMMON_KEYWORDS.join(" OR ") + ")",
            '("AIzaSy" AND (gemini OR aistudio OR chat OR prompt OR genai OR bearer OR token OR "v1beta") NOT (maps OR firebase OR android OR youtube OR adsense))',
        ],
    },
    { name: "Grok", keywords: ["grok api key", "gk_"] },
    {
        name: "Claude",
        keywords: ["claude api key", "anthropic api key", "sk-ant-"],
    },
];

export const SEARCH_SECTION = "code"; // Currently, only 'code' section is searched