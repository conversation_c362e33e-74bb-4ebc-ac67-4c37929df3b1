import { API_KEY_PATTERNS } from "../constants";

class KeyExtractor {
    /**
     * Extracts potential API keys from HTML content based on service-specific patterns.
     * @param {string} textContent - The text content to search within.
     * @param {string} serviceName - The name of the AI service.
     * @returns {string[]} An array of found potential keys.
     */
    public extractPotentialKeys(textContent: string, serviceName: keyof typeof API_KEY_PATTERNS): string[] {
        const patterns = API_KEY_PATTERNS[serviceName];
        if (!patterns) return [];

        let foundKeys = new Set<string>(); // Use a Set to automatically handle duplicates within this pass
        patterns.forEach((pattern) => {
            const currentRegex = new RegExp(pattern.source, pattern.flags); // Create a new instance
            let match;
            while ((match = currentRegex.exec(textContent)) !== null) {
                // Use the new instance
                foundKeys.add(match[0]);
            }
        });
        return Array.from(foundKeys);
    }
}

export default KeyExtractor;